# APK Pentesting Free Course 📱🔒

Welcome to the comprehensive APK Pentesting course! This course covers essential mobile application security testing techniques, focusing on Android APK analysis and penetration testing.

## 📋 Course Overview

This course provides hands-on training in mobile application security testing, covering the complete workflow from APK acquisition to vulnerability exploitation. You'll learn industry-standard tools and techniques used by security professionals.

### 🎯 Learning Objectives
- Master ADB (Android Debug Bridge) connections and device management
- Learn APK decompilation and static analysis techniques
- Understand runtime injection and dynamic analysis
- Perform comprehensive app debugging
- Identify and exploit Mobile OWASP Top 10 vulnerabilities

## 📚 Course Modules

### Module 1: ADB Connections 🔌
Learn to establish and manage Android Debug Bridge connections for device interaction.

**Topics Covered:**
- ADB installation and setup
- Device connection methods (USB, WiFi, Emulator)
- ADB command fundamentals
- Device management and troubleshooting

## 🔧 Complete ADB Command Reference

### Basic ADB Commands
```bash
# Check ADB version and help
adb version                           # Display ADB version
adb help                             # Show all available commands
adb --help                           # Alternative help command

# Device Management
adb devices                          # List all connected devices
adb devices -l                       # List devices with detailed info
adb get-state                        # Get device state (device/offline/unknown)
adb get-serialno                     # Get device serial number
adb get-devpath                      # Get device path

# Server Management
adb start-server                     # Start ADB server daemon
adb kill-server                      # Kill ADB server daemon
adb reconnect                        # Reconnect to device
adb reconnect device                 # Reconnect device side
adb reconnect offline               # Reset offline/unauthorized devices

# Connection Methods
adb connect <ip_address>:<port>      # Connect via TCP/IP (default port 5555)
adb connect *************:5555      # Example WiFi connection
adb disconnect <ip_address>:<port>   # Disconnect TCP/IP connection
adb disconnect                       # Disconnect all TCP/IP connections

# Multiple Device Management
adb -s <serial_number> <command>     # Execute command on specific device
adb -s emulator-5554 shell          # Connect to specific emulator
adb -d <command>                     # Direct command to USB device
adb -e <command>                     # Direct command to emulator
```

### Advanced ADB Shell Commands
```bash
# System Information
adb shell getprop                    # Get all system properties
adb shell getprop ro.build.version.release  # Get Android version
adb shell getprop ro.product.model  # Get device model
adb shell getprop ro.product.manufacturer    # Get manufacturer
adb shell getprop ro.build.fingerprint      # Get build fingerprint
adb shell getprop ro.debuggable     # Check if device is debuggable

# Process Management
adb shell ps                         # List running processes
adb shell ps | grep <package_name>   # Find specific app process
adb shell top                        # Show running processes (top-like)
adb shell kill <pid>                 # Kill process by PID
adb shell killall <process_name>     # Kill all processes by name

# Package Management
adb shell pm list packages           # List all installed packages
adb shell pm list packages -3       # List third-party packages only
adb shell pm list packages -s       # List system packages only
adb shell pm list packages -e       # List enabled packages
adb shell pm list packages -d       # List disabled packages
adb shell pm list packages | grep <keyword>  # Search for specific package

# Detailed Package Information
adb shell pm dump <package_name>     # Dump package information
adb shell pm path <package_name>     # Get APK path of package
adb shell dumpsys package <package_name>     # Detailed package info
adb shell pm list permissions       # List all permissions
adb shell pm list permissions -d    # List dangerous permissions only

# Application Management
adb install <apk_file>               # Install APK
adb install -r <apk_file>            # Reinstall APK (keep data)
adb install -s <apk_file>            # Install to SD card
adb install -d <apk_file>            # Allow downgrade
adb install -g <apk_file>            # Grant all permissions
adb uninstall <package_name>         # Uninstall package
adb uninstall -k <package_name>      # Uninstall but keep data

# APK Extraction
adb shell pm path <package_name>     # Get APK path
adb pull /data/app/<package_name>/base.apk ./extracted.apk
adb shell pm list packages -f       # List packages with file paths
```

### File System Operations
```bash
# File Transfer
adb push <local_file> <remote_path>  # Copy file to device
adb push app.apk /sdcard/            # Example: push APK to SD card
adb pull <remote_path> <local_path>  # Copy file from device
adb pull /sdcard/Download/ ./downloads/      # Example: pull downloads

# Directory Operations
adb shell ls                         # List files in current directory
adb shell ls -la /data/data/         # List app data directories
adb shell ls -la /sdcard/            # List SD card contents
adb shell find /data/data/<package_name> -name "*.db"  # Find database files

# File Permissions and Ownership
adb shell chmod 755 <file_path>      # Change file permissions
adb shell chown <user>:<group> <file_path>   # Change ownership
adb shell stat <file_path>           # Show file statistics

# Text File Operations
adb shell cat <file_path>            # Display file contents
adb shell head -n 20 <file_path>     # Show first 20 lines
adb shell tail -n 20 <file_path>     # Show last 20 lines
adb shell grep "pattern" <file_path> # Search for pattern in file
```

### Network and Connectivity
```bash
# Network Information
adb shell netstat -an               # Show network connections
adb shell netstat -tuln             # Show listening ports
adb shell ip addr show              # Show IP addresses
adb shell ip route show             # Show routing table
adb shell ping google.com           # Test connectivity

# WiFi Management
adb shell dumpsys wifi              # WiFi system information
adb shell settings get global wifi_on        # Check WiFi status
adb shell settings put global wifi_on 1      # Enable WiFi
adb shell settings put global wifi_on 0      # Disable WiFi

# Proxy Settings
adb shell settings put global http_proxy <ip>:<port>     # Set HTTP proxy
adb shell settings put global global_http_proxy_host <ip>        # Set proxy host
adb shell settings put global global_http_proxy_port <port>      # Set proxy port
adb shell settings delete global http_proxy             # Remove proxy
adb shell settings delete global global_http_proxy_host # Remove proxy host
adb shell settings delete global global_http_proxy_port # Remove proxy port
```

### Logging and Debugging
```bash
# Logcat Commands
adb logcat                          # View system logs
adb logcat -c                       # Clear log buffer
adb logcat -d                       # Dump logs and exit
adb logcat -f <file>                # Save logs to file
adb logcat -v time                  # Show logs with timestamp
adb logcat -v threadtime            # Show logs with thread time
adb logcat -s <tag>                 # Filter by tag
adb logcat | grep <keyword>         # Filter logs by keyword

# Log Filtering Examples
adb logcat ActivityManager:I *:S    # Show only ActivityManager info logs
adb logcat *:E                      # Show only error logs
adb logcat | grep -i "error\|exception\|crash"  # Filter for errors
adb logcat | grep <package_name>    # Filter by package name

# Buffer Management
adb logcat -b main                  # Main log buffer
adb logcat -b system                # System log buffer
adb logcat -b radio                 # Radio log buffer
adb logcat -b events                # Events log buffer
adb logcat -b crash                 # Crash log buffer
```

### Screen and Input Operations
```bash
# Screen Capture
adb shell screencap /sdcard/screen.png       # Take screenshot
adb pull /sdcard/screen.png ./               # Pull screenshot to local
adb shell screencap -p | sed 's/\r$//' > screen.png  # Direct screenshot

# Screen Recording
adb shell screenrecord /sdcard/demo.mp4      # Record screen
adb shell screenrecord --time-limit 30 /sdcard/demo.mp4  # 30-second recording
adb shell screenrecord --bit-rate 6000000 /sdcard/demo.mp4  # High quality

# Input Simulation
adb shell input text "Hello World"          # Type text
adb shell input keyevent 3                  # Press HOME key
adb shell input keyevent 4                  # Press BACK key
adb shell input keyevent 26                 # Press POWER key
adb shell input tap 500 1000                # Tap at coordinates
adb shell input swipe 300 500 700 500 1000  # Swipe gesture

# Key Event Codes
# 3=HOME, 4=BACK, 26=POWER, 24=VOLUME_UP, 25=VOLUME_DOWN
# 82=MENU, 85=PLAY_PAUSE, 126=PLAY, 127=PAUSE
```

### Module 2: APK Decompilation 🔍
Master the art of reverse engineering Android applications.

**Topics Covered:**
- APK structure and components
- Static analysis techniques
- Decompilation tools and workflows
- Source code analysis

## 🔍 Complete APK Analysis Toolkit

### APK Structure Understanding
```bash
# APK is essentially a ZIP file - examine structure
unzip -l app.apk                     # List APK contents
unzip app.apk -d extracted/          # Extract APK contents

# APK Structure:
# ├── AndroidManifest.xml             # App manifest (binary)
# ├── classes.dex                     # Dalvik bytecode
# ├── classes2.dex, classes3.dex...   # Additional DEX files
# ├── resources.arsc                  # Compiled resources
# ├── META-INF/                       # Signing information
# │   ├── MANIFEST.MF
# │   ├── CERT.RSA
# │   └── CERT.SF
# ├── res/                            # Resources directory
# │   ├── layout/                     # UI layouts
# │   ├── drawable/                   # Images
# │   ├── values/                     # Strings, colors, etc.
# │   └── ...
# ├── assets/                         # Raw assets
# ├── lib/                            # Native libraries
# │   ├── arm64-v8a/
# │   ├── armeabi-v7a/
# │   └── x86/
# └── kotlin/                         # Kotlin metadata (if present)
```

### JADX - Advanced Java Decompiler
```bash
# Basic JADX Usage
jadx app.apk                         # Decompile to jadx-output/
jadx -d output_dir app.apk           # Specify output directory
jadx --show-bad-code app.apk         # Show inconsistent code
jadx --no-res app.apk                # Skip resources decompilation
jadx --no-src app.apk                # Skip source code decompilation

# Advanced JADX Options
jadx -j 8 app.apk                    # Use 8 threads for faster processing
jadx --deobf app.apk                 # Enable deobfuscation
jadx --deobf-min 3 app.apk           # Minimum length for deobfuscation
jadx --deobf-max 64 app.apk          # Maximum length for deobfuscation
jadx --escape-unicode app.apk        # Escape unicode characters
jadx --respect-bytecode-access-modifiers app.apk  # Respect access modifiers

# Export Options
jadx --export-gradle app.apk         # Export as Gradle project
jadx -e app.apk                      # Export as Eclipse project
jadx --save-as-gradle app.apk        # Save as Gradle project

# GUI Mode
jadx-gui app.apk                     # Open in GUI mode
jadx-gui --log-level DEBUG app.apk   # GUI with debug logging

# Specific Class/Package Analysis
jadx --only-class com.example.MainActivity app.apk    # Decompile specific class
jadx --only-package com.example app.apk               # Decompile specific package
```

### Apktool - Complete APK Reverse Engineering
```bash
# Basic Apktool Usage
apktool d app.apk                    # Decode APK
apktool d app.apk -o output_dir      # Specify output directory
apktool d -f app.apk                 # Force overwrite existing directory
apktool d -s app.apk                 # Skip sources (only resources)
apktool d -r app.apk                 # Skip resources (only sources)

# Advanced Decoding Options
apktool d --no-debug-info app.apk    # Remove debug information
apktool d --keep-broken-res app.apk  # Keep broken resources
apktool d --no-assets app.apk        # Skip assets decoding
apktool d --api-level 29 app.apk     # Specify API level

# Framework Management
apktool if framework-res.apk         # Install framework
apktool if framework-res.apk --tag custom  # Install with custom tag
apktool list-frameworks              # List installed frameworks

# Building APK Back
apktool b output_dir                 # Build APK from decoded directory
apktool b output_dir -o new_app.apk  # Specify output APK name
apktool b --use-aapt2 output_dir     # Use AAPT2 for building
apktool b --debug output_dir         # Build debug version

# Signing the Rebuilt APK
keytool -genkey -v -keystore my-release-key.keystore -alias alias_name -keyalg RSA -keysize 2048 -validity 10000
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore my-release-key.keystore new_app.apk alias_name
zipalign -v 4 new_app.apk aligned_app.apk
```

### dex2jar - DEX to JAR Conversion
```bash
# Basic dex2jar Usage
d2j-dex2jar app.apk                  # Convert APK to JAR
d2j-dex2jar classes.dex              # Convert DEX file to JAR
d2j-dex2jar -o output.jar app.apk    # Specify output JAR name
d2j-dex2jar --force app.apk          # Force overwrite existing JAR

# Advanced Options
d2j-dex2jar -e app.apk               # Print exception stack trace
d2j-dex2jar -n app.apk               # Don't translate local variable names
d2j-dex2jar -s app.apk               # Don't translate debug info
d2j-dex2jar --not-handle-exception app.apk  # Don't handle exceptions

# Multiple DEX Files
d2j-dex2jar classes.dex classes2.dex classes3.dex  # Convert multiple DEX files

# JAR Analysis with JD-GUI
jd-gui output.jar                    # Open JAR in JD-GUI
# Alternative: Use online decompilers like http://www.javadecompilers.com/
```

### Baksmali/Smali - Low-level Analysis
```bash
# Baksmali - Disassemble DEX to Smali
baksmali d classes.dex               # Disassemble to smali files
baksmali d -o smali_output classes.dex  # Specify output directory
baksmali d --api-level 29 classes.dex   # Specify API level
baksmali d --jobs 8 classes.dex     # Use multiple threads

# Advanced Baksmali Options
baksmali d --no-parameter-registers classes.dex     # Don't use parameter registers
baksmali d --use-locals classes.dex                 # Use local variable table
baksmali d --sequential-labels classes.dex          # Use sequential labels
baksmali d --implicit-references classes.dex        # Allow implicit references

# Smali - Assemble Smali back to DEX
smali a smali_output                 # Assemble smali files to classes.dex
smali a -o new_classes.dex smali_output  # Specify output DEX name
smali a --api-level 29 smali_output  # Specify API level

# Smali Code Analysis Examples
grep -r "const-string" smali_output/ | grep -i "password"  # Find password strings
grep -r "invoke-" smali_output/ | grep -i "crypto"        # Find crypto calls
grep -r "sget-object" smali_output/ | grep -i "key"       # Find key objects
```

### Advanced Static Analysis Tools
```bash
# AAPT - Android Asset Packaging Tool
aapt dump badging app.apk           # Show package information
aapt dump permissions app.apk       # Show permissions
aapt dump configurations app.apk    # Show configurations
aapt dump resources app.apk         # Show resources
aapt dump strings app.apk           # Show string resources
aapt dump xmltree app.apk AndroidManifest.xml  # Show manifest tree

# AAPT2 - Next generation AAPT
aapt2 dump badging app.apk          # Package information with AAPT2
aapt2 dump permissions app.apk      # Permissions with AAPT2
aapt2 dump resources app.apk        # Resources with AAPT2

# Strings Analysis
strings app.apk | grep -i "http"    # Find HTTP URLs
strings app.apk | grep -i "api"     # Find API endpoints
strings app.apk | grep -i "key"     # Find potential keys
strings app.apk | grep -E "[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}"  # Find IP addresses

# Binary Analysis with hexdump
hexdump -C app.apk | head -20        # Hex dump of APK header
hexdump -C classes.dex | head -20    # Hex dump of DEX header

# File Type Analysis
file app.apk                        # Identify file type
file classes.dex                    # Identify DEX file
binwalk app.apk                     # Analyze embedded files
```

### Automated Analysis Scripts
```bash
# Quick APK Analysis Script
#!/bin/bash
APK_FILE=$1
OUTPUT_DIR="analysis_$(basename $APK_FILE .apk)"

echo "[+] Creating analysis directory: $OUTPUT_DIR"
mkdir -p $OUTPUT_DIR

echo "[+] Basic APK information"
aapt dump badging $APK_FILE > $OUTPUT_DIR/basic_info.txt

echo "[+] Extracting APK contents"
unzip -q $APK_FILE -d $OUTPUT_DIR/extracted/

echo "[+] Decompiling with JADX"
jadx -d $OUTPUT_DIR/jadx_output $APK_FILE

echo "[+] Decoding with Apktool"
apktool d $APK_FILE -o $OUTPUT_DIR/apktool_output

echo "[+] Converting to JAR with dex2jar"
d2j-dex2jar -o $OUTPUT_DIR/app.jar $APK_FILE

echo "[+] Extracting strings"
strings $APK_FILE > $OUTPUT_DIR/strings.txt

echo "[+] Analysis complete. Results in: $OUTPUT_DIR"
```

### Manifest Analysis Deep Dive
```bash
# Extract and analyze AndroidManifest.xml
aapt dump xmltree app.apk AndroidManifest.xml > manifest_readable.xml

# Key elements to analyze:
# 1. Permissions
grep -i "permission" manifest_readable.xml

# 2. Activities and Intent Filters
grep -A 10 -B 2 "activity" manifest_readable.xml

# 3. Services
grep -A 10 -B 2 "service" manifest_readable.xml

# 4. Broadcast Receivers
grep -A 10 -B 2 "receiver" manifest_readable.xml

# 5. Content Providers
grep -A 10 -B 2 "provider" manifest_readable.xml

# 6. Exported Components (Security Risk)
grep -i "exported.*true" manifest_readable.xml

# 7. Debug Mode
grep -i "debuggable.*true" manifest_readable.xml

# 8. Backup Settings
grep -i "allowBackup" manifest_readable.xml

# 9. Network Security Config
grep -i "networkSecurityConfig" manifest_readable.xml
```

### Module 3: Runtime Injection 💉
Learn dynamic analysis and runtime manipulation techniques.

**Topics Covered:**
- Frida framework fundamentals
- JavaScript injection techniques
- Method hooking and interception
- Runtime behavior modification

## 💉 Complete Frida Framework Guide

### Frida Installation and Setup
```bash
# Install Frida tools
pip install frida-tools              # Install Frida CLI tools
pip install frida                    # Install Frida Python bindings
npm install -g frida-compile         # Install Frida compiler (optional)

# Download Frida Server for Android
# Visit: https://github.com/frida/frida/releases
# Download frida-server-<version>-android-<arch>.xz

# Setup Frida Server on Device
adb push frida-server-15.2.2-android-arm64 /data/local/tmp/frida-server
adb shell chmod 755 /data/local/tmp/frida-server
adb shell /data/local/tmp/frida-server &

# Alternative: Use Magisk Module for rooted devices
# Download frida-server Magisk module and install via Magisk Manager
```

### Basic Frida Commands
```bash
# List processes
frida-ps -U                          # List processes on USB device
frida-ps -U | grep <app_name>        # Find specific app process
frida-ps -Ua                         # List all processes (including system)
frida-ps -Uai                        # List installed applications

# Attach to process
frida -U <package_name>              # Attach to app by package name
frida -U -p <pid>                    # Attach to app by process ID
frida -U -n <process_name>           # Attach to app by process name
frida -U -f <package_name>           # Spawn and attach to app

# Load scripts
frida -U -l script.js <package_name> # Load JavaScript file
frida -U -e "console.log('Hello')" <package_name>  # Execute inline code
frida -U --no-pause -f <package_name> -l script.js  # Spawn without pausing

# Advanced options
frida -U --debug <package_name>      # Enable debug mode
frida -U --runtime=v8 <package_name> # Use V8 runtime
frida -U --eternalize <package_name> # Keep script running after detach
```

### Comprehensive Frida Scripting Examples

#### Basic Method Hooking
```javascript
// Hook a specific method
Java.perform(function() {
    var MainActivity = Java.use("com.example.MainActivity");

    MainActivity.sensitiveMethod.implementation = function(param1, param2) {
        console.log("[+] sensitiveMethod called");
        console.log("    param1: " + param1);
        console.log("    param2: " + param2);

        // Call original method
        var result = this.sensitiveMethod(param1, param2);
        console.log("    result: " + result);

        return result;
    };
});
```

#### Method Overloading Handling
```javascript
// Hook overloaded methods
Java.perform(function() {
    var MyClass = Java.use("com.example.MyClass");

    // Hook specific overload by parameter types
    MyClass.overloadedMethod.overload('java.lang.String').implementation = function(str) {
        console.log("[+] overloadedMethod(String) called: " + str);
        return this.overloadedMethod(str);
    };

    MyClass.overloadedMethod.overload('int').implementation = function(num) {
        console.log("[+] overloadedMethod(int) called: " + num);
        return this.overloadedMethod(num);
    };

    MyClass.overloadedMethod.overload('java.lang.String', 'int').implementation = function(str, num) {
        console.log("[+] overloadedMethod(String, int) called: " + str + ", " + num);
        return this.overloadedMethod(str, num);
    };
});
```

#### Constructor Hooking
```javascript
// Hook class constructors
Java.perform(function() {
    var MyClass = Java.use("com.example.MyClass");

    MyClass.$init.overload('java.lang.String').implementation = function(param) {
        console.log("[+] MyClass constructor called with: " + param);
        return this.$init(param);
    };
});
```

#### Field Access and Modification
```javascript
// Access and modify class fields
Java.perform(function() {
    var MyClass = Java.use("com.example.MyClass");

    MyClass.someMethod.implementation = function() {
        console.log("[+] Before - privateField: " + this.privateField.value);

        // Modify field value
        this.privateField.value = "modified_value";

        console.log("[+] After - privateField: " + this.privateField.value);

        return this.someMethod();
    };
});
```

#### Advanced SSL Pinning Bypass
```javascript
// Comprehensive SSL pinning bypass
Java.perform(function() {
    console.log("[+] Starting SSL pinning bypass");

    // Hook X509TrustManager
    var X509TrustManager = Java.use('javax.net.ssl.X509TrustManager');
    var SSLContext = Java.use('javax.net.ssl.SSLContext');

    // Create custom TrustManager
    var TrustManager = Java.registerClass({
        name: 'com.frida.TrustManager',
        implements: [X509TrustManager],
        methods: {
            checkClientTrusted: function(chain, authType) {
                console.log('[+] checkClientTrusted bypassed');
            },
            checkServerTrusted: function(chain, authType) {
                console.log('[+] checkServerTrusted bypassed');
            },
            getAcceptedIssuers: function() {
                return [];
            }
        }
    });

    // Hook SSLContext.init
    SSLContext.init.overload('[Ljavax.net.ssl.KeyManager;', '[Ljavax.net.ssl.TrustManager;', 'java.security.SecureRandom').implementation = function(keyManager, trustManager, secureRandom) {
        console.log('[+] SSLContext.init called, bypassing with custom TrustManager');
        this.init(keyManager, [TrustManager.$new()], secureRandom);
    };

    // Hook OkHttp Certificate Pinner
    try {
        var CertificatePinner = Java.use('okhttp3.CertificatePinner');
        CertificatePinner.check.overload('java.lang.String', 'java.util.List').implementation = function(hostname, peerCertificates) {
            console.log('[+] OkHttp CertificatePinner.check bypassed for: ' + hostname);
            return;
        };
    } catch(e) {
        console.log('[-] OkHttp CertificatePinner not found');
    }

    // Hook Network Security Policy
    try {
        var NetworkSecurityPolicy = Java.use('android.security.NetworkSecurityPolicy');
        NetworkSecurityPolicy.getInstance.implementation = function() {
            console.log('[+] NetworkSecurityPolicy.getInstance called');
            return Java.cast(this.getInstance(), NetworkSecurityPolicy);
        };

        NetworkSecurityPolicy.isCertificateTransparencyVerificationRequired.implementation = function(hostname) {
            console.log('[+] Certificate transparency verification bypassed for: ' + hostname);
            return false;
        };
    } catch(e) {
        console.log('[-] NetworkSecurityPolicy not found');
    }
});
```

#### Root Detection Bypass
```javascript
// Comprehensive root detection bypass
Java.perform(function() {
    console.log("[+] Starting root detection bypass");

    // Common root detection methods
    var rootDetectionMethods = [
        'isDeviceRooted',
        'isRooted',
        'checkRoot',
        'detectRoot',
        'isJailbroken'
    ];

    // Hook common root detection libraries
    var rootDetectionClasses = [
        'com.scottyab.rootbeer.RootBeer',
        'com.noshufou.android.su.Su',
        'com.thirdparty.superuser.util.Utils',
        'com.raizlabs.android.dbflow.sql.language.SQLite'
    ];

    rootDetectionClasses.forEach(function(className) {
        try {
            var clazz = Java.use(className);
            rootDetectionMethods.forEach(function(methodName) {
                try {
                    clazz[methodName].implementation = function() {
                        console.log('[+] ' + className + '.' + methodName + ' bypassed');
                        return false;
                    };
                } catch(e) {
                    // Method not found, continue
                }
            });
        } catch(e) {
            // Class not found, continue
        }
    });

    // Hook file system checks
    var File = Java.use('java.io.File');
    File.exists.implementation = function() {
        var path = this.getAbsolutePath();
        if (path.indexOf('/system/bin/su') !== -1 ||
            path.indexOf('/system/xbin/su') !== -1 ||
            path.indexOf('/system/app/Superuser.apk') !== -1 ||
            path.indexOf('/data/data/com.noshufou.android.su') !== -1) {
            console.log('[+] File.exists bypassed for: ' + path);
            return false;
        }
        return this.exists();
    };

    // Hook Runtime.exec for command execution
    var Runtime = Java.use('java.lang.Runtime');
    Runtime.exec.overload('java.lang.String').implementation = function(command) {
        if (command.indexOf('su') !== -1 || command.indexOf('which') !== -1) {
            console.log('[+] Runtime.exec bypassed for: ' + command);
            throw Java.use('java.io.IOException').$new('Command not found');
        }
        return this.exec(command);
    };
});
```

#### Anti-Debugging Bypass
```javascript
// Comprehensive anti-debugging bypass
Java.perform(function() {
    console.log("[+] Starting anti-debugging bypass");

    // Hook Debug class
    var Debug = Java.use('android.os.Debug');
    Debug.isDebuggerConnected.implementation = function() {
        console.log('[+] Debug.isDebuggerConnected bypassed');
        return false;
    };

    // Hook ApplicationInfo flags
    var ApplicationInfo = Java.use('android.content.pm.ApplicationInfo');
    ApplicationInfo.flags.implementation = function() {
        var flags = this.flags.value;
        var FLAG_DEBUGGABLE = 2;
        if ((flags & FLAG_DEBUGGABLE) !== 0) {
            console.log('[+] ApplicationInfo.FLAG_DEBUGGABLE removed');
            flags = flags & ~FLAG_DEBUGGABLE;
        }
        return flags;
    };

    // Hook ptrace detection (native level)
    var libc = Module.findExportByName("libc.so", "ptrace");
    if (libc) {
        Interceptor.attach(libc, {
            onEnter: function(args) {
                console.log('[+] ptrace called, arguments: ' + args[0] + ', ' + args[1] + ', ' + args[2] + ', ' + args[3]);
            },
            onLeave: function(retval) {
                console.log('[+] ptrace bypassed, returning 0');
                retval.replace(0);
            }
        });
    }

    // Hook TracerPid check
    var fopen = Module.findExportByName("libc.so", "fopen");
    if (fopen) {
        Interceptor.attach(fopen, {
            onEnter: function(args) {
                var path = Memory.readUtf8String(args[0]);
                if (path.indexOf("/proc/") !== -1 && path.indexOf("/status") !== -1) {
                    console.log('[+] fopen called for: ' + path);
                    this.is_proc_status = true;
                }
            },
            onLeave: function(retval) {
                if (this.is_proc_status) {
                    console.log('[+] /proc/*/status access bypassed');
                    retval.replace(0);
                }
            }
        });
    }
});
```

#### Memory and Heap Analysis
```javascript
// Memory scanning and modification
Java.perform(function() {
    console.log("[+] Starting memory analysis");

    // Scan for specific strings in memory
    function scanMemoryForString(targetString) {
        var ranges = Process.enumerateRanges('r--');
        ranges.forEach(function(range) {
            try {
                Memory.scan(range.base, range.size, targetString, {
                    onMatch: function(address, size) {
                        console.log('[+] Found "' + targetString + '" at: ' + address);
                        console.log('    Context: ' + Memory.readUtf8String(address, 50));
                    },
                    onComplete: function() {
                        // Scan complete for this range
                    }
                });
            } catch(e) {
                // Range not readable, continue
            }
        });
    }

    // Hook memory allocation
    var malloc = Module.findExportByName("libc.so", "malloc");
    if (malloc) {
        Interceptor.attach(malloc, {
            onEnter: function(args) {
                this.size = args[0].toInt32();
            },
            onLeave: function(retval) {
                if (this.size > 1024) {  // Log large allocations
                    console.log('[+] Large malloc: ' + this.size + ' bytes at ' + retval);
                }
            }
        });
    }

    // Example: Scan for API keys
    scanMemoryForString("api_key");
    scanMemoryForString("secret");
    scanMemoryForString("token");
});
```

#### Dynamic Class Loading and Reflection
```javascript
// Hook dynamic class loading and reflection
Java.perform(function() {
    console.log("[+] Hooking dynamic class loading");

    // Hook Class.forName
    var Class = Java.use('java.lang.Class');
    Class.forName.overload('java.lang.String').implementation = function(className) {
        console.log('[+] Class.forName called for: ' + className);
        return this.forName(className);
    };

    // Hook Method.invoke
    var Method = Java.use('java.lang.reflect.Method');
    Method.invoke.overload('java.lang.Object', '[Ljava.lang.Object;').implementation = function(obj, args) {
        console.log('[+] Method.invoke called: ' + this.getName());
        console.log('    on object: ' + obj);
        console.log('    with args: ' + args);
        return this.invoke(obj, args);
    };

    // Hook DexClassLoader
    var DexClassLoader = Java.use('dalvik.system.DexClassLoader');
    DexClassLoader.$init.implementation = function(dexPath, optimizedDirectory, librarySearchPath, parent) {
        console.log('[+] DexClassLoader loading: ' + dexPath);
        return this.$init(dexPath, optimizedDirectory, librarySearchPath, parent);
    };
});
```

### Module 4: App Debugging 🐛
Comprehensive debugging techniques for mobile applications.

**Topics Covered:**
- Dynamic debugging setup
- Breakpoint management
- Memory analysis
- Network traffic interception

**Tools Used:**
- **GDB** - GNU Debugger
- **LLDB** - LLVM Debugger
- **Burp Suite** - Web application security testing
- **OWASP ZAP** - Security testing proxy

### Module 5: Mobile OWASP Top 10 🛡️
Deep dive into the most critical mobile application security risks.

**Vulnerabilities Covered:**
1. **M1: Improper Platform Usage**
2. **M2: Insecure Data Storage**
3. **M3: Insecure Communication**
4. **M4: Insecure Authentication**
5. **M5: Insufficient Cryptography**
6. **M6: Insecure Authorization**
7. **M7: Client Code Quality**
8. **M8: Code Tampering**
9. **M9: Reverse Engineering**
10. **M10: Extraneous Functionality**

## 🔄 APK Pentesting Workflow

```mermaid
graph TD
    A[Target APK] --> B[Environment Setup]
    B --> C[ADB Connection]
    C --> D[APK Acquisition]
    D --> E[Static Analysis]
    E --> F[Dynamic Analysis]
    F --> G[Vulnerability Assessment]
    G --> H[Exploitation]
    H --> I[Reporting]
    
    E --> E1[APK Decompilation]
    E1 --> E2[Source Code Review]
    E2 --> E3[Manifest Analysis]
    E3 --> E4[Resource Examination]
    
    F --> F1[Runtime Injection]
    F1 --> F2[Method Hooking]
    F2 --> F3[Traffic Interception]
    F3 --> F4[Behavior Analysis]
    
    G --> G1[OWASP Top 10 Check]
    G1 --> G2[Custom Vulnerability Scan]
    G2 --> G3[Risk Assessment]
```

## 🛠️ Required Tools Installation

### Prerequisites
- Android SDK Platform Tools
- Java Development Kit (JDK)
- Python 3.x
- Node.js (for Frida)

### Tool Installation Commands
```bash
# Install ADB (Android SDK Platform Tools)
# Download from: https://developer.android.com/studio/releases/platform-tools

# Install JADX
wget https://github.com/skylot/jadx/releases/latest/download/jadx-1.4.7.zip
unzip jadx-1.4.7.zip

# Install Apktool
wget https://raw.githubusercontent.com/iBotPeaches/Apktool/master/scripts/linux/apktool
chmod +x apktool

# Install Frida
pip install frida-tools

# Install dex2jar
wget https://github.com/pxb1988/dex2jar/releases/download/v2.1/dex2jar-2.1.zip
unzip dex2jar-2.1.zip
```

## 📱 Lab Environment Setup

### Android Emulator Configuration
1. Install Android Studio
2. Create AVD (Android Virtual Device)
3. Enable Developer Options
4. Configure USB Debugging

### Physical Device Setup
1. Enable Developer Options
2. Enable USB Debugging
3. Install USB drivers
4. Verify ADB connection

## 🎓 Hands-on Labs

### Lab 1: Basic ADB Operations
- Connect to Android device
- Install and uninstall APKs
- Extract APK from device
- Analyze system logs

### Lab 2: APK Static Analysis
- Decompile target APK
- Analyze AndroidManifest.xml
- Review source code for vulnerabilities
- Extract sensitive information

### Lab 3: Dynamic Analysis with Frida
- Set up Frida server
- Write JavaScript hooks
- Intercept method calls
- Modify runtime behavior

### Lab 4: Network Traffic Analysis
- Configure proxy settings
- Intercept HTTPS traffic
- Bypass SSL pinning
- Analyze API communications

### Lab 5: OWASP Top 10 Assessment
- Identify insecure data storage
- Test authentication mechanisms
- Analyze cryptographic implementations
- Assess authorization controls

## 🔍 Detailed Module Breakdown

### ADB Connection Flow Chart

```mermaid
graph TD
    A[Start] --> B[Install ADB]
    B --> C[Enable Developer Options]
    C --> D[Enable USB Debugging]
    D --> E[Connect Device]
    E --> F{Connection Type?}

    F -->|USB| G[USB Connection]
    F -->|WiFi| H[WiFi Connection]
    F -->|Emulator| I[Emulator Connection]

    G --> J[adb devices]
    H --> K[adb connect IP:PORT]
    I --> L[Start Emulator]

    J --> M{Device Listed?}
    K --> M
    L --> M

    M -->|Yes| N[Connection Successful]
    M -->|No| O[Troubleshoot]

    O --> P[Check Drivers]
    P --> Q[Restart ADB Server]
    Q --> R[Check Device Authorization]
    R --> M

    N --> S[Ready for Testing]
```

### APK Decompilation Workflow

```mermaid
graph TD
    A[APK File] --> B{Analysis Type?}

    B -->|Static| C[Static Analysis]
    B -->|Dynamic| D[Dynamic Analysis]

    C --> E[Extract APK]
    E --> F[Decompile with JADX]
    F --> G[Analyze Manifest]
    G --> H[Review Source Code]
    H --> I[Check Resources]
    I --> J[Identify Vulnerabilities]

    D --> K[Install APK]
    K --> L[Setup Frida]
    L --> M[Hook Functions]
    M --> N[Monitor Behavior]
    N --> O[Intercept Traffic]
    O --> P[Analyze Runtime Data]

    J --> Q[Generate Report]
    P --> Q
```

### Runtime Injection Process

```mermaid
graph TD
    A[Target App] --> B[Frida Server Setup]
    B --> C[Identify Target Process]
    C --> D[Attach to Process]
    D --> E[Load JavaScript Hook]
    E --> F{Hook Type?}

    F -->|Method Hook| G[Intercept Method Calls]
    F -->|Class Hook| H[Monitor Class Instantiation]
    F -->|Native Hook| I[Hook Native Functions]

    G --> J[Modify Parameters]
    H --> K[Track Object Creation]
    I --> L[Intercept System Calls]

    J --> M[Log Results]
    K --> M
    L --> M

    M --> N[Analyze Behavior]
    N --> O[Identify Vulnerabilities]
```

## 🔐 Mobile OWASP Top 10 Deep Dive

### M1: Improper Platform Usage
**Description:** Misuse of platform features or failure to use platform security controls.

**Common Issues:**
- Improper use of TouchID/FaceID
- Misuse of keychain services
- Incorrect permission usage
- Insecure inter-app communication

**Testing Approach:**
```bash
# Check app permissions
adb shell dumpsys package <package_name> | grep permission

# Analyze manifest for dangerous permissions
grep -i "permission" AndroidManifest.xml

# Test intent filters
adb shell am start -a <action> -d <data>
```

### M2: Insecure Data Storage
**Description:** Sensitive data stored insecurely on the device.

**Common Locations:**
- SQLite databases
- Log files
- Plist files
- Temp directories
- SD card storage

**Testing Commands:**
```bash
# Find app data directory
adb shell run-as <package_name> ls -la

# Check for sensitive data in logs
adb logcat | grep -i "password\|token\|key"

# Examine SQLite databases
adb shell run-as <package_name> sqlite3 databases/app.db
.tables
.schema
SELECT * FROM sensitive_table;
```

### M3: Insecure Communication
**Description:** Poor handshaking, incorrect SSL versions, weak negotiation, cleartext transmission.

**Testing Areas:**
- Network traffic analysis
- Certificate validation
- SSL/TLS implementation
- API endpoint security

**Tools and Techniques:**
```bash
# Setup proxy for traffic interception
adb shell settings put global http_proxy <proxy_ip>:<port>

# Monitor network connections
adb shell netstat -an

# Check for cleartext traffic
tcpdump -i any -s 0 -w capture.pcap
```

### M4: Insecure Authentication
**Description:** Weak authentication schemes that allow attackers to bypass authentication.

**Common Weaknesses:**
- Weak password policies
- Insecure credential storage
- Poor session management
- Biometric bypass vulnerabilities

### M5: Insufficient Cryptography
**Description:** Weak encryption algorithms, poor key management, or custom crypto implementations.

**Analysis Points:**
- Encryption algorithms used
- Key generation and storage
- Random number generation
- Cryptographic protocol implementation

## 🧪 Advanced Testing Techniques

### SSL Pinning Bypass
```javascript
// Frida script for SSL pinning bypass
Java.perform(function() {
    var X509TrustManager = Java.use('javax.net.ssl.X509TrustManager');
    var SSLContext = Java.use('javax.net.ssl.SSLContext');

    X509TrustManager.checkClientTrusted.implementation = function(chain, authType) {
        console.log('[+] Bypassing SSL pinning');
    };

    X509TrustManager.checkServerTrusted.implementation = function(chain, authType) {
        console.log('[+] Bypassing SSL pinning');
    };
});
```

### Root Detection Bypass
```javascript
// Frida script for root detection bypass
Java.perform(function() {
    var RootBeer = Java.use('com.scottyab.rootbeer.RootBeer');

    RootBeer.isRooted.implementation = function() {
        console.log('[+] Root detection bypassed');
        return false;
    };
});
```

### Anti-Debugging Bypass
```javascript
// Frida script for anti-debugging bypass
Java.perform(function() {
    var Debug = Java.use('android.os.Debug');

    Debug.isDebuggerConnected.implementation = function() {
        console.log('[+] Debugger detection bypassed');
        return false;
    };
});
```

## 📊 Comprehensive Testing Methodology

### Pre-Assessment Phase
```mermaid
graph TD
    A[Information Gathering] --> B[Target Identification]
    B --> C[Environment Setup]
    C --> D[Tool Preparation]
    D --> E[Baseline Testing]

    A --> A1[App Store Analysis]
    A --> A2[Developer Information]
    A --> A3[Version History]

    B --> B1[Package Name]
    B --> B2[Version Details]
    B --> B3[Permissions Required]

    C --> C1[Test Device Setup]
    C --> C2[Proxy Configuration]
    C --> C3[Certificate Installation]

    D --> D1[Static Analysis Tools]
    D --> D2[Dynamic Analysis Tools]
    D --> D3[Network Analysis Tools]
```

### Assessment Execution Flow
```mermaid
graph TD
    A[Start Assessment] --> B[Static Analysis]
    B --> C[Dynamic Analysis]
    C --> D[Network Analysis]
    D --> E[Vulnerability Validation]
    E --> F[Impact Assessment]
    F --> G[Report Generation]

    B --> B1[Code Review]
    B --> B2[Manifest Analysis]
    B --> B3[Resource Examination]
    B --> B4[Library Assessment]

    C --> C1[Runtime Behavior]
    C --> C2[Memory Analysis]
    C --> C3[File System Monitoring]
    C --> C4[API Interaction]

    D --> D1[Traffic Interception]
    D --> D2[Protocol Analysis]
    D --> D3[Encryption Assessment]
    D --> D4[Authentication Testing]
```

## 🎯 Practical Testing Scenarios

### Scenario 1: Banking Application Assessment
**Objective:** Assess a mobile banking application for security vulnerabilities.

**Testing Steps:**
1. **Static Analysis**
   ```bash
   # Decompile APK
   jadx -d output_dir banking_app.apk

   # Analyze for hardcoded credentials
   grep -r "password\|secret\|key" output_dir/

   # Check for debugging flags
   grep -r "debuggable\|allowBackup" output_dir/
   ```

2. **Dynamic Analysis**
   ```bash
   # Monitor file system access
   frida -U -l monitor_filesystem.js com.bank.app

   # Intercept cryptographic operations
   frida -U -l crypto_monitor.js com.bank.app
   ```

3. **Network Analysis**
   ```bash
   # Setup Burp Suite proxy
   adb shell settings put global http_proxy *************:8080

   # Monitor API calls
   frida -U -l api_monitor.js com.bank.app
   ```

### Scenario 2: Social Media Application Testing
**Focus Areas:**
- Data privacy and storage
- Authentication mechanisms
- Social engineering vulnerabilities
- Third-party integrations

### Scenario 3: E-commerce Application Security
**Key Testing Points:**
- Payment processing security
- Session management
- Input validation
- Business logic flaws

## 🔧 Custom Tool Development

### Automated APK Analysis Script
```python
#!/usr/bin/env python3
import os
import subprocess
import json

class APKAnalyzer:
    def __init__(self, apk_path):
        self.apk_path = apk_path
        self.results = {}

    def static_analysis(self):
        """Perform static analysis using multiple tools"""
        # JADX decompilation
        subprocess.run(['jadx', '-d', 'output', self.apk_path])

        # Manifest analysis
        self.analyze_manifest()

        # String analysis
        self.extract_strings()

        # Permission analysis
        self.analyze_permissions()

    def analyze_manifest(self):
        """Analyze AndroidManifest.xml for security issues"""
        manifest_path = 'output/resources/AndroidManifest.xml'
        # Implementation details...

    def extract_strings(self):
        """Extract and analyze strings for sensitive data"""
        # Implementation details...

    def analyze_permissions(self):
        """Analyze requested permissions"""
        # Implementation details...

    def generate_report(self):
        """Generate comprehensive security report"""
        # Implementation details...

if __name__ == "__main__":
    analyzer = APKAnalyzer("target_app.apk")
    analyzer.static_analysis()
    analyzer.generate_report()
```

## 📚 Additional Resources

### Essential Reading
- [OWASP Mobile Security Testing Guide](https://owasp.org/www-project-mobile-security-testing-guide/)
- [Android Security Internals](https://nostarch.com/androidsecurity)
- [Mobile Application Penetration Testing](https://www.packtpub.com/product/mobile-application-penetration-testing/*************)

### Online Resources
- [OWASP Mobile Top 10](https://owasp.org/www-project-mobile-top-10/)
- [Android Developers Security](https://developer.android.com/topic/security)
- [Frida Documentation](https://frida.re/docs/)

### Practice Platforms
- [DIVA Android](https://github.com/payatu/diva-android) - Damn Insecure and Vulnerable App
- [InsecureBankv2](https://github.com/dineshshetty/Android-InsecureBankv2)
- [OWASP UnCrackable Apps](https://github.com/OWASP/owasp-mstg/tree/master/Crackmes)

### Community and Forums
- [OWASP Mobile Security](https://owasp.org/www-project-mobile-security/)
- [Android Security Reddit](https://www.reddit.com/r/AndroidSecurity/)
- [XDA Developers](https://forum.xda-developers.com/)

## 🏆 Certification and Career Path

### Relevant Certifications
- **OSCP** - Offensive Security Certified Professional
- **CEH** - Certified Ethical Hacker
- **CISSP** - Certified Information Systems Security Professional
- **GSEC** - GIAC Security Essentials

### Career Opportunities
- Mobile Application Security Analyst
- Penetration Tester
- Security Consultant
- Bug Bounty Hunter
- Security Researcher

## 📝 Assessment Checklist

### Pre-Assessment
- [ ] Environment setup complete
- [ ] Target application identified
- [ ] Testing scope defined
- [ ] Tools installed and configured
- [ ] Legal authorization obtained

### Static Analysis
- [ ] APK decompiled successfully
- [ ] Manifest file analyzed
- [ ] Source code reviewed
- [ ] Hardcoded secrets identified
- [ ] Third-party libraries assessed

### Dynamic Analysis
- [ ] Runtime behavior monitored
- [ ] Memory analysis performed
- [ ] File system access tracked
- [ ] Network traffic intercepted
- [ ] API interactions analyzed

### Vulnerability Assessment
- [ ] OWASP Top 10 vulnerabilities checked
- [ ] Custom vulnerability tests performed
- [ ] Business logic flaws identified
- [ ] Security controls bypassed
- [ ] Impact assessment completed

### Reporting
- [ ] Executive summary prepared
- [ ] Technical findings documented
- [ ] Proof of concept developed
- [ ] Remediation recommendations provided
- [ ] Risk ratings assigned

## 🚀 Getting Started

1. **Clone this repository**
   ```bash
   git clone https://github.com/your-repo/apk-pentesting-course.git
   cd apk-pentesting-course
   ```

2. **Set up your environment**
   ```bash
   ./setup.sh
   ```

3. **Start with Module 1: ADB Connections**
   - Follow the step-by-step guide
   - Complete hands-on exercises
   - Practice with provided examples

4. **Progress through each module systematically**
   - Master each concept before moving forward
   - Complete all lab exercises
   - Apply knowledge to real-world scenarios

## 🤝 Contributing

We welcome contributions to improve this course! Please:
1. Fork the repository
2. Create a feature branch
3. Submit a pull request with detailed description

## 📄 License

This course is released under the MIT License. See [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This course is for educational purposes only. Always ensure you have proper authorization before testing any applications. The authors are not responsible for any misuse of the information provided.

---

**Happy Learning and Stay Secure! 🔒**
